import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { ERAttribute } from '../../types/erDiagram';
import styles from './EntityNode.module.css';

// 实体节点的数据类型
export interface EntityNodeData {
  label: string;
  description?: string;
  attributes: ERAttribute[];
}

// 实体节点组件
const EntityNode: React.FC<NodeProps<EntityNodeData>> = ({ data, selected }) => {
  const { label, description, attributes } = data;

  return (
    <div className={`${styles.entityNode} ${selected ? styles.selected : ''}`}>
      {/* 连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className={styles.handle}
        style={{ top: '-4px', left: '50%', transform: 'translateX(-50%)' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className={styles.handle}
        style={{ top: '50%', right: '-4px', transform: 'translateY(-50%)' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className={styles.handle}
        style={{ bottom: '-4px', left: '50%', transform: 'translateX(-50%)' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        className={styles.handle}
        style={{ top: '50%', left: '-4px', transform: 'translateY(-50%)' }}
      />

      {/* 实体标题 */}
      <div className={styles.header}>
        <div className={styles.title}>{label}</div>
        {description && (
          <div className={styles.description}>{description}</div>
        )}
      </div>

      {/* 属性列表 */}
      <div className={styles.attributesList}>
        {attributes.map((attr) => (
          <div
            key={attr.id}
            className={`${styles.attribute} ${attr.isPrimaryKey ? styles.primaryKey : ''}`}
          >
            <div className={styles.attributeInfo}>
              <span className={styles.attributeName}>
                {attr.isPrimaryKey && <span className={styles.keyIcon}>PK</span>}
                {attr.name}
              </span>
              {attr.dataType && (
                <span className={styles.dataType}>{attr.dataType}</span>
              )}
            </div>
            {attr.isRequired && (
              <span className={styles.required}>*</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EntityNode;
