import React, { useMemo, useCallback, useState } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  Panel,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider,
  useReactFlow
} from '@xyflow/react';
import { Box, Typography, Paper, Chip, Tooltip, IconButton } from '@mui/material';
import { 
  ZoomIn as ZoomInIcon, 
  ZoomOut as ZoomOutIcon, 
  CenterFocusStrong as CenterIcon,
  Info as InfoIcon 
} from '@mui/icons-material';

import { ERDiagramData } from '../../types/erDiagram';
import { convertERJsonToFlow, LayoutConfig } from '../../utils/erToFlow';
import EntityNode from './EntityNode';
import DiamondNode from './DiamondNode';
import styles from './ERDiagram.module.css';

import '@xyflow/react/dist/style.css';

// 自定义节点类型
const nodeTypes = {
  entity: EntityNode,
  diamond: DiamondNode,
};

// ER图组件的属性接口
interface ERDiagramProps {
  data: ERDiagramData;
  layoutConfig?: Partial<LayoutConfig>;
  className?: string;
  showMiniMap?: boolean;
  showControls?: boolean;
  showBackground?: boolean;
  onNodeClick?: (node: Node) => void;
  onEdgeClick?: (edge: Edge) => void;
}

// 自定义控制面板组件
const CustomControls: React.FC = () => {
  const { zoomIn, zoomOut, fitView } = useReactFlow();

  return (
    <Panel position="top-right" className={styles.customControls}>
      <Tooltip title="放大">
        <IconButton 
          size="small" 
          onClick={() => zoomIn({ duration: 800 })}
          className={styles.controlButton}
        >
          <ZoomInIcon />
        </IconButton>
      </Tooltip>
      <Tooltip title="缩小">
        <IconButton 
          size="small" 
          onClick={() => zoomOut({ duration: 800 })}
          className={styles.controlButton}
        >
          <ZoomOutIcon />
        </IconButton>
      </Tooltip>
      <Tooltip title="适应视图">
        <IconButton 
          size="small" 
          onClick={() => fitView({ duration: 800, padding: 0.2 })}
          className={styles.controlButton}
        >
          <CenterIcon />
        </IconButton>
      </Tooltip>
    </Panel>
  );
};

// 信息面板组件
const InfoPanel: React.FC<{ data: ERDiagramData }> = ({ data }) => {
  const [showInfo, setShowInfo] = useState(false);

  return (
    <Panel position="top-left" className={styles.infoPanel}>
      <Tooltip title={showInfo ? "隐藏信息" : "显示信息"}>
        <IconButton 
          size="small" 
          onClick={() => setShowInfo(!showInfo)}
          className={styles.infoButton}
        >
          <InfoIcon />
        </IconButton>
      </Tooltip>
      
      {showInfo && (
        <Paper className={styles.infoContent} elevation={3}>
          <Typography variant="h6" className={styles.infoTitle}>
            {data.metadata?.title || 'ER图'}
          </Typography>
          
          {data.metadata?.description && (
            <Typography variant="body2" className={styles.infoDescription}>
              {data.metadata.description}
            </Typography>
          )}
          
          <Box className={styles.statsContainer}>
            <Chip 
              label={`实体: ${data.entities.length}`} 
              size="small" 
              color="primary" 
              variant="outlined"
            />
            <Chip 
              label={`关系: ${data.relationships.length}`} 
              size="small" 
              color="secondary" 
              variant="outlined"
            />
          </Box>
          
          {data.metadata?.version && (
            <Typography variant="caption" className={styles.version}>
              版本: {data.metadata.version}
            </Typography>
          )}
        </Paper>
      )}
    </Panel>
  );
};

// 主ER图组件
const ERDiagramComponent: React.FC<ERDiagramProps> = ({
  data,
  layoutConfig,
  className,
  showMiniMap = true,
  showControls = true,
  showBackground = true,
  onNodeClick,
  onEdgeClick
}) => {
  // 转换数据为React Flow格式
  const { nodes: initialNodes, edges: initialEdges } = useMemo(() => {
    return convertERJsonToFlow(data, layoutConfig);
  }, [data, layoutConfig]);

  // 使用React Flow的状态管理
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // 节点点击处理
  const handleNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('Node clicked:', node);
    onNodeClick?.(node);
  }, [onNodeClick]);

  // 边点击处理
  const handleEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    console.log('Edge clicked:', edge);
    onEdgeClick?.(edge);
  }, [onEdgeClick]);

  return (
    <div className={`${styles.erDiagram} ${className || ''}`}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={handleNodeClick}
        onEdgeClick={handleEdgeClick}
        nodeTypes={nodeTypes}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
        }}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        attributionPosition="bottom-left"
      >
        {/* 自定义控制面板 */}
        {showControls && <CustomControls />}
        
        {/* 信息面板 */}
        <InfoPanel data={data} />
        
        {/* 默认控制组件 */}
        <Controls 
          className={styles.defaultControls}
          showZoom={false}
          showFitView={false}
          showInteractive={true}
        />
        
        {/* 小地图 */}
        {showMiniMap && (
          <MiniMap 
            className={styles.miniMap}
            nodeColor={(node) => {
              switch (node.type) {
                case 'entity':
                  return '#2196f3';
                case 'diamond':
                  return '#0277bd';
                default:
                  return '#999';
              }
            }}
            maskColor="rgba(255, 255, 255, 0.2)"
            pannable
            zoomable
          />
        )}
        
        {/* 背景 */}
        {showBackground && (
          <Background
            variant={BackgroundVariant.Dots}
            gap={20}
            size={1}
            className={styles.background}
          />
        )}
      </ReactFlow>
    </div>
  );
};

// 带Provider的包装组件
const ERDiagram: React.FC<ERDiagramProps> = (props) => {
  return (
    <ReactFlowProvider>
      <ERDiagramComponent {...props} />
    </ReactFlowProvider>
  );
};

export default ERDiagram;
