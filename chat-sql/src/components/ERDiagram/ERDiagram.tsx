import React, { useMemo, useCallback, useState, useEffect } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  Panel,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider,
  useReactFlow
} from '@xyflow/react';
import { Box, Typography, Paper, Chip, Tooltip, IconButton } from '@mui/material';
import { 
  ZoomIn as ZoomInIcon, 
  ZoomOut as ZoomOutIcon, 
  CenterFocusStrong as CenterIcon,
  Info as InfoIcon 
} from '@mui/icons-material';

import { ERDiagramData } from '../../types/erDiagram';
import { convertERJsonToFlow, LayoutConfig } from '../../utils/erToFlow';
import EntityNode from './EntityNode';
import DiamondNode from './DiamondNode';
import TotalParticipationEdge from './TotalParticipationEdge';
import styles from './ERDiagram.module.css';

import '@xyflow/react/dist/style.css';

// 自定义节点类型
const nodeTypes = {
  entity: EntityNode,
  diamond: DiamondNode,
};

// 自定义边类型
const edgeTypes = {
  totalParticipationEdge: TotalParticipationEdge,
};

// ER图组件的属性接口
interface ERDiagramProps {
  data: ERDiagramData;
  layoutConfig?: Partial<LayoutConfig>;
  className?: string;
  showMiniMap?: boolean;
  showControls?: boolean;
  showBackground?: boolean;
  onNodeClick?: (node: Node) => void;
  onEdgeClick?: (edge: Edge) => void;
}

// 自定义控制面板组件
const CustomControls: React.FC = () => {
  const { zoomIn, zoomOut, fitView } = useReactFlow();

  return (
    <Panel position="bottom-right" className={styles.customControls}>
      <Tooltip title="放大" placement="top">
        <button
          type="button"
          onClick={() => zoomIn({ duration: 800 })}
          className={styles.controlButton}
          aria-label="放大"
        >
          +
        </button>
      </Tooltip>
      <Tooltip title="缩小" placement="top">
        <button
          type="button"
          onClick={() => zoomOut({ duration: 800 })}
          className={styles.controlButton}
          aria-label="缩小"
        >
          -
        </button>
      </Tooltip>
      <Tooltip title="适应视图" placement="top">
        <button
          type="button"
          onClick={() => fitView({ duration: 800, padding: 0.2 })}
          className={`${styles.controlButton} ${styles.fitButton}`}
          aria-label="适应视图"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
          </svg>
        </button>
      </Tooltip>
    </Panel>
  );
};

// 信息面板组件
const InfoPanel: React.FC<{ data: ERDiagramData }> = ({ data }) => {
  const [showInfo, setShowInfo] = useState(false);

  return (
    <Panel position="top-left" className={styles.infoPanel}>
      <Tooltip title={showInfo ? "隐藏信息" : "显示信息"}>
        <IconButton 
          size="small" 
          onClick={() => setShowInfo(!showInfo)}
          className={styles.infoButton}
        >
          <InfoIcon />
        </IconButton>
      </Tooltip>
      
      {showInfo && (
        <Paper className={styles.infoContent} elevation={3}>
          <Typography variant="h6" className={styles.infoTitle}>
            {data.metadata?.title || 'ER图'}
          </Typography>
          
          {data.metadata?.description && (
            <Typography variant="body2" className={styles.infoDescription}>
              {data.metadata.description}
            </Typography>
          )}
          
          <Box className={styles.statsContainer}>
            <Chip 
              label={`实体: ${data.entities.length}`} 
              size="small" 
              color="primary" 
              variant="outlined"
            />
            <Chip 
              label={`关系: ${data.relationships.length}`} 
              size="small" 
              color="secondary" 
              variant="outlined"
            />
          </Box>
          
          {data.metadata?.version && (
            <Typography variant="caption" className={styles.version}>
              版本: {data.metadata.version}
            </Typography>
          )}
        </Paper>
      )}
    </Panel>
  );
};

// 主ER图组件
const ERDiagramComponent: React.FC<ERDiagramProps> = ({
  data,
  layoutConfig,
  className,
  showMiniMap = true,
  showControls = true,
  showBackground = true,
  onNodeClick,
  onEdgeClick
}) => {
  // 转换数据为React Flow格式
  const { nodes: initialNodes, edges: initialEdges } = useMemo(() => {
    return convertERJsonToFlow(data, layoutConfig);
  }, [data, layoutConfig]);

  // 使用React Flow的状态管理
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // 当数据变化时，重新设置节点和边
  useEffect(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
  }, [initialNodes, initialEdges, setNodes, setEdges]);

  // 节点点击处理
  const handleNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('Node clicked:', node);
    onNodeClick?.(node);
  }, [onNodeClick]);

  // 边点击处理
  const handleEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    console.log('Edge clicked:', edge);
    onEdgeClick?.(edge);
  }, [onEdgeClick]);

  return (
    <div className={`${styles.erDiagram} ${className || ''}`}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={handleNodeClick}
        onEdgeClick={handleEdgeClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
        }}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        attributionPosition="bottom-left"
      >
        {/* 自定义控制面板 */}
        {showControls && <CustomControls />}
        
        {/* 信息面板 */}
        <InfoPanel data={data} />
        
        {/* 默认控制组件 */}
        <Controls 
          className={styles.defaultControls}
          showZoom={false}
          showFitView={false}
          showInteractive={true}
        />
        
        {/* 小地图 */}
        {showMiniMap && (
          <MiniMap 
            className={styles.miniMap}
            nodeColor={(node) => {
              switch (node.type) {
                case 'entity':
                  return '#2196f3';
                case 'diamond':
                  return '#0277bd';
                default:
                  return '#999';
              }
            }}
            maskColor="rgba(255, 255, 255, 0.2)"
            pannable
            zoomable
          />
        )}
        
        {/* 背景 */}
        {showBackground && (
          <Background
            variant={BackgroundVariant.Dots}
            gap={20}
            size={1}
            className={styles.background}
          />
        )}
      </ReactFlow>
    </div>
  );
};

// 带Provider的包装组件
const ERDiagram: React.FC<ERDiagramProps> = (props) => {
  return (
    <ReactFlowProvider>
      <ERDiagramComponent {...props} />
    </ReactFlowProvider>
  );
};

export default ERDiagram;
