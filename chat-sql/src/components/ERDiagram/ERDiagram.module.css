/* ER图主容器样式 */
.erDiagram {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 自定义控制面板 */
.customControls {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.controlButton {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  color: #666 !important;
  transition: all 0.2s ease !important;
}

.controlButton:hover {
  background: #f5f5f5 !important;
  border-color: #2196f3 !important;
  color: #2196f3 !important;
}

/* 信息面板 */
.infoPanel {
  z-index: 10;
}

.infoButton {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  color: #666 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.infoButton:hover {
  background: #f5f5f5 !important;
  border-color: #2196f3 !important;
  color: #2196f3 !important;
}

.infoContent {
  margin-top: 8px;
  padding: 16px;
  min-width: 250px;
  max-width: 300px;
}

.infoTitle {
  color: #1976d2;
  margin-bottom: 8px;
  font-weight: 600;
}

.infoDescription {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.statsContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.version {
  color: #999;
  display: block;
  margin-top: 8px;
}

/* 默认控制组件样式覆盖 */
.defaultControls {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.defaultControls button {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  color: #666 !important;
  transition: all 0.2s ease !important;
}

.defaultControls button:hover {
  background: #f5f5f5 !important;
  border-color: #2196f3 !important;
  color: #2196f3 !important;
}

/* 小地图样式 */
.miniMap {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 背景样式 */
.background {
  background-color: #fafafa;
}

/* React Flow 样式覆盖 */
.erDiagram .react-flow__edge-path {
  stroke: #2196f3;
  stroke-width: 2;
}

.erDiagram .react-flow__edge.selected .react-flow__edge-path {
  stroke: #1976d2;
  stroke-width: 3;
}

.erDiagram .react-flow__edge-label {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 600;
  color: #1976d2;
}

.erDiagram .react-flow__edge-labelBg {
  fill: white;
  fill-opacity: 0.9;
}

/* 连接线动画 */
.erDiagram .react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* 选择框样式 */
.erDiagram .react-flow__selection {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid #2196f3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customControls {
    padding: 6px;
    gap: 2px;
  }
  
  .infoContent {
    min-width: 200px;
    max-width: 250px;
    padding: 12px;
  }
  
  .infoTitle {
    font-size: 1rem;
  }
  
  .infoDescription {
    font-size: 0.875rem;
  }
  
  .statsContainer {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .infoContent {
    min-width: 180px;
    max-width: 220px;
    padding: 10px;
  }
  
  .customControls {
    padding: 4px;
  }
  
  .controlButton,
  .infoButton {
    min-width: 32px !important;
    width: 32px !important;
    height: 32px !important;
  }
}
