/* 实体节点样式 */
.entityNode {
  background: white;
  border: 2px solid #2196f3;
  border-radius: 8px;
  min-width: 200px;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.entityNode:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #1976d2;
}

.entityNode.selected {
  border-color: #0d47a1;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

.header {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
  padding: 12px 16px;
  text-align: center;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1.2;
}

.description {
  font-size: 12px;
  opacity: 0.9;
  line-height: 1.2;
}

.attributesList {
  padding: 0;
  margin: 0;
}

.attribute {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.attribute:last-child {
  border-bottom: none;
}

.attribute:hover {
  background-color: #f5f5f5;
}

.attribute.primaryKey {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.attribute.primaryKey:hover {
  background-color: #ffecb3;
}

.attribute.foreignKey {
  background-color: #f3e5f5;
  border-left: 4px solid #9c27b0;
}

.attribute.foreignKey:hover {
  background-color: #e1bee7;
}

.attributeInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.attributeName {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
}

.keyIcon {
  font-size: 12px;
}

.dataType {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.required {
  color: #f44336;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
}

.handle {
  width: 8px !important;
  height: 8px !important;
  background: #2196f3 !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
}

.handle:hover {
  width: 12px !important;
  height: 12px !important;
  background: #1976d2 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .entityNode {
    min-width: 160px;
    max-width: 240px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .description {
    font-size: 11px;
  }
  
  .attributeName {
    font-size: 13px;
  }
  
  .dataType {
    font-size: 11px;
  }
  
  .attribute {
    padding: 6px 12px;
  }
}
