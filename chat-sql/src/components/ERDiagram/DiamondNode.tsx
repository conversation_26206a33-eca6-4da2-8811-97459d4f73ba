import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import styles from './DiamondNode.module.css';

// 菱形节点的数据类型
export interface DiamondNodeData {
  label: string;
  description?: string;
  attributes?: Array<{
    id: string;
    name: string;
    dataType?: string;
  }>;
}

// 菱形节点组件
const DiamondNode: React.FC<NodeProps<DiamondNodeData>> = ({ data, selected }) => {
  const { label, description, attributes } = data;
  
  // 菱形的尺寸
  const width = 160;
  const height = 100;
  const centerX = width / 2;
  const centerY = height / 2;
  
  // 菱形的四个顶点坐标
  const points = [
    `${centerX},10`,           // 上顶点
    `${width - 10},${centerY}`, // 右顶点
    `${centerX},${height - 10}`, // 下顶点
    `10,${centerY}`            // 左顶点
  ].join(' ');

  return (
    <div className={`${styles.diamondNode} ${selected ? styles.selected : ''}`}>
      {/* 上方连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className={styles.handle}
        style={{ 
          top: '10px', 
          left: '50%', 
          transform: 'translateX(-50%)',
          background: '#555'
        }}
      />
      
      {/* 右侧连接点 */}
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className={styles.handle}
        style={{ 
          top: '50%', 
          right: '10px', 
          transform: 'translateY(-50%)',
          background: '#555'
        }}
      />
      
      {/* 下方连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className={styles.handle}
        style={{ 
          bottom: '10px', 
          left: '50%', 
          transform: 'translateX(-50%)',
          background: '#555'
        }}
      />
      
      {/* 左侧连接点 */}
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        className={styles.handle}
        style={{ 
          top: '50%', 
          left: '10px', 
          transform: 'translateY(-50%)',
          background: '#555'
        }}
      />

      {/* SVG 菱形 */}
      <svg 
        width={width} 
        height={height} 
        className={styles.diamondSvg}
        viewBox={`0 0 ${width} ${height}`}
      >
        <polygon
          points={points}
          className={styles.diamondShape}
          fill="#e1f5fe"
          stroke="#0277bd"
          strokeWidth="2"
        />
      </svg>
      
      {/* 标签文本 */}
      <div className={styles.labelContainer}>
        <div className={styles.label}>{label}</div>
        {description && (
          <div className={styles.description}>{description}</div>
        )}
      </div>
      
      {/* 关系属性（如果有的话） */}
      {attributes && attributes.length > 0 && (
        <div className={styles.attributesContainer}>
          {attributes.map((attr) => (
            <div key={attr.id} className={styles.attribute}>
              <span className={styles.attributeName}>{attr.name}</span>
              {attr.dataType && (
                <span className={styles.attributeType}>: {attr.dataType}</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DiamondNode;
